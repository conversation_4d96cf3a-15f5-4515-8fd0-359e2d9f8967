// Translation data
const translations = {
    en: {
        // Navigation
        'Home': 'Home',
        'Applications': 'Applications',
        'Features': 'Features',
        'Contact': 'Contact',

        // Hero Section
        'MedLink': 'MedLink',
        'Healthcare Management': 'Healthcare Management',
        'Revolutionized': 'Revolutionized',
        'hero-description': 'Comprehensive digital solutions for dental clinics, medical practices, and pharmacies. Experience the future of healthcare management with real-time data updates and intuitive interfaces.',
        'Explore Applications': 'Explore Applications',
        'Contact Us': 'Contact Us',

        // Applications Section
        'Our Applications': 'Our Applications',
        'applications-subtitle': 'Three powerful solutions designed for modern healthcare professionals',
        'Dental Clinic Management': 'Dental Clinic Management',
        'Medical Clinic Management': 'Medical Clinic Management',
        'Pharmacy Accounting System': 'Pharmacy Accounting System',

        // Features Section
        'Why Choose MedLink?': 'Why Choose MedLink?',
        'features-subtitle': 'Advanced features that set us apart from the competition',
        'Real-Time Updates': 'Real-Time Updates',
        'Mobile Responsive': 'Mobile Responsive',
        'Secure & Compliant': 'Secure & Compliant',
        'Analytics Dashboard': 'Analytics Dashboard',
        '24/7 Support': '24/7 Support',
        'Cloud-Based': 'Cloud-Based',

        // Contact Section
        'Get In Touch': 'Get In Touch',
        'contact-subtitle': 'Ready to transform your healthcare practice? Contact us today!',
        'Email': 'Email',
        'Phone': 'Phone',
        'Address': 'Address',
        'Follow Us': 'Follow Us',

        // Footer
        'footer-text': '© 2024 MedLink. All rights reserved. Revolutionizing healthcare management.'
    },
    ar: {
        // Navigation
        'Home': 'الرئيسية',
        'Applications': 'التطبيقات',
        'Features': 'المميزات',
        'Contact': 'اتصل بنا',

        // Hero Section
        'Revolutionary Healthcare Solutions': 'حلول الرعاية الصحية الثورية',
        'MedLink': 'مِدلِنك',
        'Healthcare Management': 'إدارة الرعاية الصحية',
        'Revolutionized': 'ثورة حقيقية',
        'hero-description': 'حوّل ممارستك الصحية بحلولنا الرقمية الشاملة. مصممة لعيادات الأسنان والممارسات الطبية والصيدليات مع التحديثات الفورية والواجهات البديهية.',
        'real-time updates': 'التحديثات الفورية',
        'intuitive interfaces': 'الواجهات البديهية',
        'Happy Clients': 'عميل سعيد',
        'Uptime': 'وقت التشغيل',
        'Support': 'الدعم',
        'Explore Applications': 'استكشف التطبيقات',
        'Watch Demo': 'شاهد العرض التوضيحي',
        'Scroll to explore': 'مرر للاستكشاف',

        // Hero Cards
        'Dental': 'الأسنان',
        'Complete clinic management': 'إدارة شاملة للعيادة',
        'Medical': 'الطب',
        'Electronic health records': 'السجلات الصحية الإلكترونية',
        'Pharmacy': 'الصيدلة',
        'Inventory & accounting': 'المخزون والمحاسبة',

        // Applications Section
        'Our Applications': 'تطبيقاتنا',
        'applications-subtitle': 'ثلاثة حلول قوية مصممة لمتخصصي الرعاية الصحية الحديثة',
        'Dental Clinic Management': 'إدارة عيادة الأسنان',
        'dental-description': 'حل شامل لإدارة الممارسة السنية مع سجلات المرضى وجدولة المواعيد وتخطيط العلاج وتكامل الفوترة.',
        'Patient Management System': 'نظام إدارة المرضى',
        'Appointment Scheduling': 'جدولة المواعيد',
        'Treatment Planning': 'تخطيط العلاج',
        'Digital X-ray Integration': 'تكامل الأشعة السينية الرقمية',
        'Billing & Insurance': 'الفوترة والتأمين',

        'Medical Clinic Management': 'إدارة العيادة الطبية',
        'medical-description': 'نظام شامل لإدارة العيادة للأطباء مع السجلات الصحية الإلكترونية وإدارة الوصفات وتنسيق رعاية المرضى.',
        'Electronic Health Records': 'السجلات الصحية الإلكترونية',
        'Prescription Management': 'إدارة الوصفات',
        'Lab Results Integration': 'تكامل نتائج المختبر',
        'Telemedicine Support': 'دعم الطب عن بُعد',
        'Multi-Doctor Workflow': 'سير عمل متعدد الأطباء',

        'Pharmacy Accounting System': 'نظام محاسبة الصيدلية',
        'pharmacy-description': 'برنامج محاسبة مبسط وقوي مصمم خصيصاً للصيادلة مع إدارة المخزون والتتبع المالي الفوري.',
        'Inventory Management': 'إدارة المخزون',
        'Sales Tracking': 'تتبع المبيعات',
        'Supplier Management': 'إدارة الموردين',
        'Financial Reports': 'التقارير المالية',
        'Expiry Date Alerts': 'تنبيهات تاريخ الانتهاء',

        // Features Section
        'Why Choose MedLink?': 'لماذا تختار مِدلِنك؟',
        'features-subtitle': 'ميزات متقدمة تميزنا عن المنافسة',
        'Real-Time Updates': 'التحديثات الفورية',
        'real-time-description': 'مزامنة البيانات تحدث فوراً عبر جميع الأجهزة والمنصات، مما يضمن حصولك دائماً على أحدث المعلومات.',
        'Mobile Responsive': 'متجاوب مع الجوال',
        'mobile-description': 'الوصول إلى تطبيقاتك من أي جهاز - سطح المكتب أو الجهاز اللوحي أو الهاتف الذكي مع الوظائف الكاملة.',
        'Secure & Compliant': 'آمن ومتوافق',
        'security-description': 'متوافق مع HIPAA مع أمان على مستوى المؤسسة لحماية بيانات المرضى والأعمال الحساسة.',
        'Analytics Dashboard': 'لوحة التحليلات',
        'analytics-description': 'تقارير وتحليلات شاملة لمساعدتك في اتخاذ قرارات مدروسة لممارستك.',
        '24/7 Support': 'دعم على مدار الساعة',
        'support-description': 'دعم فني وخدمة عملاء على مدار الساعة لضمان سير عملياتك بسلاسة.',
        'Cloud-Based': 'قائم على السحابة',
        'cloud-description': 'بنية تحتية سحابية آمنة مع نسخ احتياطية تلقائية وتحديثات سلسة دون توقف.',

        // Contact Section
        'Get In Touch': 'تواصل معنا',
        'contact-subtitle': 'مستعد لتحويل ممارستك الصحية؟ اتصل بنا اليوم!',
        'Email': 'البريد الإلكتروني',
        'Phone': 'الهاتف',
        'Address': 'العنوان',
        'Follow Us': 'تابعنا',
        'Instagram': 'إنستغرام',
        'Facebook': 'فيسبوك',
        'WhatsApp': 'واتساب',
        'Gmail': 'جيميل',

        // Footer
        'footer-text': '© 2024 مِدلِنك. جميع الحقوق محفوظة. ثورة في إدارة الرعاية الصحية.'
    }
};

let currentLanguage = 'en';

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeScrollEffects();
    initializeAnimations();
    initializeInteractiveElements();
    initializeParallax();
    initializeMouseEffects();
    initializeTranslation();
});

// Navigation Functions
function initializeNavigation() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');
    const navbar = document.getElementById('navbar');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Navbar scroll effect with hide/show functionality
    let lastScrollTop = 0;
    window.addEventListener('scroll', function() {
        const currentScroll = window.pageYOffset || document.documentElement.scrollTop;

        if (currentScroll > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Hide/show navbar based on scroll direction
        if (currentScroll > lastScrollTop && currentScroll > 100) {
            // Scrolling down & past 100px
            navbar.classList.add('navbar-hidden');
        } else {
            // Scrolling up
            navbar.classList.remove('navbar-hidden');
        }

        lastScrollTop = currentScroll <= 0 ? 0 : currentScroll; // For Mobile or negative scrolling
    });

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 70;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}



// Scroll Effects
function initializeScrollEffects() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.app-card, .feature-card, .contact-item');
    animateElements.forEach(el => observer.observe(el));
}

// Interactive Animations
function initializeAnimations() {
    // Floating shapes animation
    const shapes = document.querySelectorAll('.shape');
    shapes.forEach((shape, index) => {
        shape.style.animationDelay = `${index * 0.5}s`;
    });

    // Medical icons pulse animation
    const iconContainers = document.querySelectorAll('.icon-container');
    iconContainers.forEach((container, index) => {
        container.style.animationDelay = `${index * 0.3}s`;
    });

    // Typing effect for hero title
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        typeWriter(heroTitle);
    }
}

// Typing effect function
function typeWriter(element) {
    const text = element.innerHTML;
    element.innerHTML = '';
    element.style.opacity = '1';
    
    let i = 0;
    const timer = setInterval(function() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
        } else {
            clearInterval(timer);
        }
    }, 50);
}

// Interactive Elements
function initializeInteractiveElements() {
    // App cards hover effects
    const appCards = document.querySelectorAll('.app-card');
    appCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Feature cards interactive effects
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.feature-icon');
            icon.style.transform = 'rotate(360deg) scale(1.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.feature-icon');
            icon.style.transform = 'rotate(0deg) scale(1)';
        });
    });

    // Social links hover effects
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.05)';
        });
        
        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Button click effects
    const buttons = document.querySelectorAll('.btn, .app-btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Parallax Effects
function initializeParallax() {
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.floating-shapes');

        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });

        // Background pattern parallax
        const backgroundElements = document.querySelectorAll('.applications::before, .contact::before, .features::before');
        backgroundElements.forEach((element, index) => {
            const speed = 0.1 + (index * 0.05);
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });

        // Dynamic background color shift based on scroll
        const scrollPercent = scrolled / (document.body.scrollHeight - window.innerHeight);
        const hue = Math.floor(scrollPercent * 60 + 200); // Shift from blue to purple
        document.documentElement.style.setProperty('--dynamic-hue', hue);
    });
}

// Utility Functions
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const offsetTop = section.offsetTop - 70;
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// Counter Animation
function animateCounter(element, target, duration = 2000) {
    let start = 0;
    const increment = target / (duration / 16);
    
    const timer = setInterval(() => {
        start += increment;
        element.textContent = Math.floor(start);
        
        if (start >= target) {
            element.textContent = target;
            clearInterval(timer);
        }
    }, 16);
}

// Form Validation (if needed for future contact form)
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
        } else {
            input.classList.remove('error');
        }
    });
    
    return isValid;
}

// Loading Screen (optional)
function showLoadingScreen() {
    const loader = document.createElement('div');
    loader.className = 'loader';
    loader.innerHTML = `
        <div class="loader-content">
            <i class="fas fa-heartbeat"></i>
            <p>Loading MedLink...</p>
        </div>
    `;
    document.body.appendChild(loader);
    
    setTimeout(() => {
        loader.remove();
    }, 1500);
}

// Scroll to top functionality
function addScrollToTop() {
    const scrollBtn = document.createElement('button');
    scrollBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollBtn.className = 'scroll-to-top';
    scrollBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--gradient-primary);
        border: none;
        color: white;
        cursor: pointer;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 1000;
    `;
    
    document.body.appendChild(scrollBtn);
    
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            scrollBtn.style.opacity = '1';
        } else {
            scrollBtn.style.opacity = '0';
        }
    });
    
    scrollBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Mouse Effects for Interactive Backgrounds
function initializeMouseEffects() {
    let mouseX = 0;
    let mouseY = 0;

    document.addEventListener('mousemove', function(e) {
        mouseX = e.clientX / window.innerWidth;
        mouseY = e.clientY / window.innerHeight;

        // Update CSS custom properties for mouse position
        document.documentElement.style.setProperty('--mouse-x', mouseX);
        document.documentElement.style.setProperty('--mouse-y', mouseY);

        // Create subtle parallax effect on floating medical elements
        const medicalElements = document.querySelectorAll('.medical-element');
        medicalElements.forEach((element, index) => {
            const speed = 0.02 + (index * 0.01);
            const x = (mouseX - 0.5) * speed * 100;
            const y = (mouseY - 0.5) * speed * 100;
            element.style.transform += ` translate(${x}px, ${y}px)`;
        });
    });

    // Add mouse interaction to hero section
    const hero = document.querySelector('.hero');
    if (hero) {
        hero.addEventListener('mousemove', function(e) {
            const rect = hero.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = (e.clientY - rect.top) / rect.height;

            // Update hero background position based on mouse
            const heroBackground = hero.querySelector('.hero-background');
            if (heroBackground) {
                heroBackground.style.transform = `translate(${x * 10}px, ${y * 10}px)`;
            }
        });
    }
}

// Initialize scroll to top
document.addEventListener('DOMContentLoaded', addScrollToTop);

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .error {
        border-color: #ef4444 !important;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    }
`;
document.head.appendChild(style);

// Translation Functions
function initializeTranslation() {
    const translateBtn = document.getElementById('translate-btn');
    const translateText = translateBtn.querySelector('.translate-text');

    translateBtn.addEventListener('click', function() {
        currentLanguage = currentLanguage === 'en' ? 'ar' : 'en';
        translatePage();

        // Update button text
        translateText.textContent = currentLanguage === 'en' ? 'عربي' : 'English';
        translateBtn.title = currentLanguage === 'en' ? 'Translate to Arabic' : 'Translate to English';
    });
}

function translatePage() {
    // Apply language attribute to body
    document.body.setAttribute('lang', currentLanguage);

    // Navigation
    const navElements = [
        { selector: '.nav-link[href="#home"]', key: 'Home' },
        { selector: '.nav-link[href="#applications"]', key: 'Applications' },
        { selector: '.nav-link[href="#features"]', key: 'Features' },
        { selector: '.nav-link[href="#contact"]', key: 'Contact' }
    ];

    navElements.forEach(item => {
        const element = document.querySelector(item.selector);
        if (element && translations[currentLanguage][item.key]) {
            element.textContent = translations[currentLanguage][item.key];
        }
    });

    // Hero Section
    translateElement('.hero-badge span', 'Revolutionary Healthcare Solutions');
    translateElement('.hero-description', 'hero-description');

    // Hero buttons with icon preservation
    const primaryBtn = document.querySelector('.hero-btn-primary');
    if (primaryBtn && translations[currentLanguage]['Explore Applications']) {
        const icon = primaryBtn.querySelector('i');
        const iconHTML = icon ? icon.outerHTML : '<i class="fas fa-rocket"></i>';
        primaryBtn.innerHTML = `${iconHTML} <span>${translations[currentLanguage]['Explore Applications']}</span><div class="btn-shine"></div>`;
    }

    const secondaryBtn = document.querySelector('.hero-btn-secondary');
    if (secondaryBtn && translations[currentLanguage]['Watch Demo']) {
        const icon = secondaryBtn.querySelector('i');
        const iconHTML = icon ? icon.outerHTML : '<i class="fas fa-play"></i>';
        secondaryBtn.innerHTML = `${iconHTML} <span>${translations[currentLanguage]['Watch Demo']}</span>`;
    }

    translateElement('.hero-scroll-indicator span', 'Scroll to explore');

    // Hero Stats
    translateElement('.stat-item:nth-child(1) .stat-label', 'Happy Clients');
    translateElement('.stat-item:nth-child(2) .stat-label', 'Uptime');
    translateElement('.stat-item:nth-child(3) .stat-label', 'Support');

    // Hero Cards with icon preservation
    const heroCards = [
        { selector: '.card-dental', titleKey: 'Dental', descKey: 'Complete clinic management', icon: 'fas fa-tooth' },
        { selector: '.card-medical', titleKey: 'Medical', descKey: 'Electronic health records', icon: 'fas fa-stethoscope' },
        { selector: '.card-pharmacy', titleKey: 'Pharmacy', descKey: 'Inventory & accounting', icon: 'fas fa-pills' }
    ];

    heroCards.forEach(card => {
        const cardElement = document.querySelector(card.selector);
        if (cardElement) {
            const title = cardElement.querySelector('h3');
            const description = cardElement.querySelector('p');
            const iconContainer = cardElement.querySelector('.card-icon');

            if (title && translations[currentLanguage][card.titleKey]) {
                title.textContent = translations[currentLanguage][card.titleKey];
            }

            if (description && translations[currentLanguage][card.descKey]) {
                description.textContent = translations[currentLanguage][card.descKey];
            }

            // Ensure icon is preserved
            if (iconContainer) {
                const icon = iconContainer.querySelector('i');
                if (!icon) {
                    iconContainer.innerHTML = `<i class="${card.icon}"></i>`;
                }
            }
        }
    });

    // Applications Section
    const appsTitle = document.querySelector('.applications .section-title');
    if (appsTitle && translations[currentLanguage]['Our Applications']) {
        if (currentLanguage === 'ar') {
            appsTitle.innerHTML = `${translations[currentLanguage]['Our Applications']} <span class="gradient-text">مِدلِنك</span>`;
        } else {
            appsTitle.innerHTML = `${translations[currentLanguage]['Our Applications']} <span class="gradient-text">MedLink</span>`;
        }
    }

    translateElement('.applications .section-subtitle', 'applications-subtitle');
    translateElement('.dental-app h3', 'Dental Clinic Management');
    translateElement('.dental-app p', 'dental-description');
    translateElement('.medical-app h3', 'Medical Clinic Management');
    translateElement('.medical-app p', 'medical-description');
    translateElement('.pharmacy-app h3', 'Pharmacy Accounting System');
    translateElement('.pharmacy-app p', 'pharmacy-description');

    // Application Features
    const appFeatures = [
        // Dental features
        { selector: '.dental-app .app-features li:nth-child(1)', key: 'Patient Management System' },
        { selector: '.dental-app .app-features li:nth-child(2)', key: 'Appointment Scheduling' },
        { selector: '.dental-app .app-features li:nth-child(3)', key: 'Treatment Planning' },
        { selector: '.dental-app .app-features li:nth-child(4)', key: 'Digital X-ray Integration' },
        { selector: '.dental-app .app-features li:nth-child(5)', key: 'Billing & Insurance' },

        // Medical features
        { selector: '.medical-app .app-features li:nth-child(1)', key: 'Electronic Health Records' },
        { selector: '.medical-app .app-features li:nth-child(2)', key: 'Prescription Management' },
        { selector: '.medical-app .app-features li:nth-child(3)', key: 'Lab Results Integration' },
        { selector: '.medical-app .app-features li:nth-child(4)', key: 'Telemedicine Support' },
        { selector: '.medical-app .app-features li:nth-child(5)', key: 'Multi-Doctor Workflow' },

        // Pharmacy features
        { selector: '.pharmacy-app .app-features li:nth-child(1)', key: 'Inventory Management' },
        { selector: '.pharmacy-app .app-features li:nth-child(2)', key: 'Sales Tracking' },
        { selector: '.pharmacy-app .app-features li:nth-child(3)', key: 'Supplier Management' },
        { selector: '.pharmacy-app .app-features li:nth-child(4)', key: 'Financial Reports' },
        { selector: '.pharmacy-app .app-features li:nth-child(5)', key: 'Expiry Date Alerts' }
    ];

    appFeatures.forEach(item => {
        const element = document.querySelector(item.selector);
        if (element && translations[currentLanguage][item.key]) {
            const icon = element.querySelector('i');
            const iconHTML = icon ? icon.outerHTML : '<i class="fas fa-check"></i>';
            element.innerHTML = `${iconHTML} ${translations[currentLanguage][item.key]}`;
        }
    });

    // Features Section
    const featuresTitle = document.querySelector('.features .section-title');
    if (featuresTitle && translations[currentLanguage]['Why Choose MedLink?']) {
        if (currentLanguage === 'ar') {
            featuresTitle.innerHTML = translations[currentLanguage]['Why Choose MedLink?'].replace('مِدلِنك', '<span class="gradient-text">مِدلِنك</span>');
        } else {
            featuresTitle.innerHTML = `Why Choose <span class="gradient-text">MedLink</span>?`;
        }
    }

    translateElement('.features .section-subtitle', 'features-subtitle');

    // Feature Cards with icon preservation
    const featureCardsData = [
        { index: 1, titleKey: 'Real-Time Updates', descKey: 'real-time-description', icon: 'fas fa-sync-alt' },
        { index: 2, titleKey: 'Mobile Responsive', descKey: 'mobile-description', icon: 'fas fa-mobile-alt' },
        { index: 3, titleKey: 'Secure & Compliant', descKey: 'security-description', icon: 'fas fa-shield-alt' },
        { index: 4, titleKey: 'Analytics Dashboard', descKey: 'analytics-description', icon: 'fas fa-chart-line' },
        { index: 5, titleKey: '24/7 Support', descKey: 'support-description', icon: 'fas fa-headset' },
        { index: 6, titleKey: 'Cloud-Based', descKey: 'cloud-description', icon: 'fas fa-cloud' }
    ];

    featureCardsData.forEach(card => {
        const featureCard = document.querySelector(`.feature-card:nth-child(${card.index})`);
        if (featureCard) {
            const title = featureCard.querySelector('h3');
            const description = featureCard.querySelector('p');

            if (title && translations[currentLanguage][card.titleKey]) {
                title.textContent = translations[currentLanguage][card.titleKey];
            }

            if (description && translations[currentLanguage][card.descKey]) {
                description.textContent = translations[currentLanguage][card.descKey];
            }

            // Ensure icon is preserved
            const iconContainer = featureCard.querySelector('.feature-icon');
            if (iconContainer) {
                const icon = iconContainer.querySelector('i');
                if (!icon) {
                    iconContainer.innerHTML = `<i class="${card.icon}"></i>`;
                }
            }
        }
    });

    // Contact Section
    const contactTitle = document.querySelector('.contact .section-title');
    if (contactTitle && translations[currentLanguage]['Get In Touch']) {
        if (currentLanguage === 'ar') {
            const parts = translations[currentLanguage]['Get In Touch'].split(' ');
            contactTitle.innerHTML = `${parts[0]} <span class="gradient-text">${parts.slice(1).join(' ')}</span>`;
        } else {
            contactTitle.innerHTML = `Get In <span class="gradient-text">Touch</span>`;
        }
    }

    translateElement('.contact .section-subtitle', 'contact-subtitle');

    // Contact Items
    const contactItems = [
        { selector: '.contact-item:nth-child(1) h3', key: 'Email' },
        { selector: '.contact-item:nth-child(2) h3', key: 'Phone' },
        { selector: '.contact-item:nth-child(3) h3', key: 'Address' },
        { selector: '.social-media h3', key: 'Follow Us' }
    ];

    contactItems.forEach(item => {
        translateElement(item.selector, item.key);
    });

    // Social Links with icon preservation
    const socialLinks = [
        { selector: '.social-link.instagram', key: 'Instagram', icon: 'fab fa-instagram' },
        { selector: '.social-link.facebook', key: 'Facebook', icon: 'fab fa-facebook-f' },
        { selector: '.social-link.whatsapp', key: 'WhatsApp', icon: 'fab fa-whatsapp' },
        { selector: '.social-link.gmail', key: 'Gmail', icon: 'fas fa-envelope' }
    ];

    socialLinks.forEach(link => {
        const element = document.querySelector(link.selector);
        if (element && translations[currentLanguage][link.key]) {
            const icon = element.querySelector('i');
            const iconHTML = icon ? icon.outerHTML : `<i class="${link.icon}"></i>`;
            element.innerHTML = `${iconHTML} <span>${translations[currentLanguage][link.key]}</span>`;
        }
    });

    // Footer
    translateElement('.footer-content p', 'footer-text');
}

function translateElement(selector, key) {
    const element = document.querySelector(selector);
    if (element && translations[currentLanguage][key]) {
        element.textContent = translations[currentLanguage][key];
    }
}
