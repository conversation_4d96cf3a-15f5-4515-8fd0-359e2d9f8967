/* Font Declarations */
@font-face {
    font-family: 'Nexa';
    src: url('font/Nexa-Heavy.ttf') format('truetype');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme (Default) */
    --dynamic-hue: 220; /* Default blue hue */
    --mouse-x: 0.5; /* Mouse X position (0-1) */
    --mouse-y: 0.5; /* Mouse Y position (0-1) */
    --primary-color: #003875;
    --secondary-color: #4A73D3;
    --accent-color: #4A73D3;
    --orange-color: #FF6B35;
    --orange-light: #FF8C42;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #003875;
    --light-color: #f8fafc;
    --text-primary: #FFFFFF;
    --text-secondary: #FFFFFF;
    --bg-color: #003875;
    --card-bg: #4A73D3;
    --footer-bg: #003875;
    --footer-text: #FFFFFF;
    --border-color: #4A73D3;
    --gradient-primary: linear-gradient(135deg, #003875 0%, #4A73D3 100%);
    --gradient-secondary: linear-gradient(135deg, #4A73D3 0%, #003875 100%);
    --gradient-accent: linear-gradient(135deg, #4A73D3 0%, #003875 100%);
    /* Dynamic gradients based on scroll position */
    --gradient-dynamic: linear-gradient(135deg,
                        #003875 0%,
                        #4A73D3 100%);
    /* Mouse-responsive gradient */
    --gradient-mouse: radial-gradient(circle at calc(var(--mouse-x) * 100%) calc(var(--mouse-y) * 100%),
                      rgba(0, 56, 117, 0.15) 0%,
                      rgba(74, 115, 211, 0.08) 50%,
                      transparent 100%);
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Theme */
[data-theme="dark"] {
    --dynamic-hue: 220; /* Consistent blue hue for dark mode */
    --primary-color: #4A73D3;
    --secondary-color: #003875;
    --accent-color: #4A73D3;
    --orange-color: #FF6B35;
    --orange-light: #FF8C42;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #003875;
    --light-color: #003875;
    --text-primary: #FFFFFF;
    --text-secondary: #FFFFFF;
    --bg-color: #003875;
    --card-bg: #4A73D3;
    --footer-bg: #003875;
    --footer-text: #FFFFFF;
    --border-color: #4A73D3;
    --gradient-primary: linear-gradient(135deg, #4A73D3 0%, #003875 100%);
    --gradient-secondary: linear-gradient(135deg, #003875 0%, #4A73D3 100%);
    --gradient-accent: linear-gradient(135deg, #4A73D3 0%, #003875 100%);
    /* Dynamic gradients based on scroll position - darker for dark mode */
    --gradient-dynamic: linear-gradient(135deg,
                        #4A73D3 0%,
                        #003875 100%);
    /* Mouse-responsive gradient for dark mode */
    --gradient-mouse: radial-gradient(circle at calc(var(--mouse-x) * 100%) calc(var(--mouse-y) * 100%),
                      rgba(74, 115, 211, 0.15) 0%,
                      rgba(0, 56, 117, 0.08) 50%,
                      transparent 100%);
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-color);
    overflow-x: hidden;
    transition: background var(--transition), color var(--transition);
    position: relative;
}

/* Dynamic Background Patterns */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    opacity: 0.08;
    background-image:
        radial-gradient(circle at 20% 20%, var(--primary-color) 1.5px, transparent 1.5px),
        radial-gradient(circle at 80% 80%, var(--accent-color) 1.5px, transparent 1.5px),
        radial-gradient(circle at 40% 60%, var(--secondary-color) 1.5px, transparent 1.5px);
    background-size: 50px 50px, 80px 80px, 60px 60px;
    background-position: 0 0, 25px 25px, 40px 40px;
    animation: backgroundShift 20s ease-in-out infinite;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background:
        var(--gradient-mouse),
        linear-gradient(45deg, transparent 30%, rgba(0, 56, 117, 0.08) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(74, 115, 211, 0.08) 50%, transparent 70%);
    background-size: 100% 100%, 200px 200px, 150px 150px;
    animation: backgroundFlow 15s linear infinite;
    transition: background var(--transition);
}

[data-theme="dark"] body::before {
    opacity: 0.08;
    background-image:
        radial-gradient(circle at 20% 20%, var(--primary-color) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, var(--accent-color) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, var(--secondary-color) 1px, transparent 1px);
}

[data-theme="dark"] body::after {
    background:
        var(--gradient-mouse),
        linear-gradient(45deg, transparent 30%, rgba(96, 165, 250, 0.05) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(34, 211, 238, 0.05) 50%, transparent 70%);
    background-size: 100% 100%, 200px 200px, 150px 150px;
}

@keyframes backgroundShift {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(10px, -10px) rotate(90deg); }
    50% { transform: translate(-5px, 15px) rotate(180deg); }
    75% { transform: translate(15px, 5px) rotate(270deg); }
}

@keyframes backgroundFlow {
    0% { transform: translateX(-100px) translateY(-100px); }
    100% { transform: translateX(100px) translateY(100px); }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(0, 56, 117, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(74, 115, 211, 0.2);
    z-index: 1000;
    transition: all 0.3s ease-in-out;
    transform: translateY(0);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-hidden {
    transform: translateY(-100%);
}

[data-theme="dark"] .navbar {
    background: rgba(0, 56, 117, 0.95);
    border-bottom: 1px solid rgba(74, 115, 211, 0.3);
}

.navbar.scrolled {
    background: rgba(0, 56, 117, 0.98);
    box-shadow: var(--shadow-light);
}

[data-theme="dark"] .navbar.scrolled {
    background: rgba(0, 56, 117, 0.98);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 75px;
    position: relative;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Translate Button */
.translate-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #FFFFFF;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.translate-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--orange-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.translate-btn i {
    font-size: 1rem;
    color: var(--orange-color);
}

.translate-text {
    font-family: 'Cairo', 'Inter', sans-serif;
    font-weight: 600;
}

/* Arabic Language Support */
[lang="ar"] {
    font-family: 'Cairo', 'Inter', sans-serif !important;
    direction: rtl;
    text-align: right;
}

[lang="ar"] * {
    font-family: 'Cairo', 'Inter', sans-serif !important;
}

[lang="ar"] .nav-menu {
    direction: rtl;
}

[lang="ar"] .nav-logo {
    flex-direction: row-reverse;
}

[lang="ar"] .nav-logo .logo-img {
    margin-right: 0;
    margin-left: 8px;
}

[lang="ar"] .hero-container {
    direction: rtl;
}

[lang="ar"] .hero-content {
    text-align: right;
}

[lang="ar"] .hero-badge {
    direction: rtl;
}

[lang="ar"] .hero-stats {
    direction: rtl;
}

[lang="ar"] .hero-actions {
    direction: rtl;
}

[lang="ar"] .hero-cards {
    direction: rtl;
}

[lang="ar"] .hero-card {
    direction: rtl;
    text-align: right;
}

[lang="ar"] .applications-grid {
    direction: rtl;
}

[lang="ar"] .app-card {
    text-align: right;
}

[lang="ar"] .features-grid {
    direction: rtl;
}

[lang="ar"] .feature-card {
    text-align: right;
}

[lang="ar"] .contact-content {
    direction: rtl;
}

[lang="ar"] .contact-item {
    text-align: right;
}

[lang="ar"] .social-links {
    direction: rtl;
}

[lang="ar"] .footer-content {
    direction: rtl;
    text-align: right;
}



.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #FFFFFF;
    text-decoration: none;
}

.nav-logo span {
    font-family: 'Nexa', 'Inter', sans-serif;
    font-weight: 800;
    letter-spacing: 0.5px;
    color: #FFFFFF;
}

.logo-img {
    width: 40px;
    height: 40px;
    margin-right: 8px;
    transition: transform 0.3s ease;
    object-fit: contain;
}

.nav-logo:hover .logo-img {
    transform: scale(1.1);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    text-decoration: none;
    color: #FFFFFF;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 0.95rem;
}

.nav-link:hover {
    color: var(--orange-color);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    border-radius: 1px;
}

.nav-link:hover::after {
    width: 80%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #FFFFFF;
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #003875 0%, #1e3a8a 50%, #4A73D3 100%);
    overflow: hidden;
}

[data-theme="dark"] .hero {
    background: linear-gradient(135deg, #003875 0%, #1e3a8a 50%, #4A73D3 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(74, 115, 211, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    z-index: 2;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle-1 {
    width: 8px;
    height: 8px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.particle-2 {
    width: 12px;
    height: 12px;
    top: 60%;
    left: 20%;
    animation-delay: 1s;
}

.particle-3 {
    width: 6px;
    height: 6px;
    top: 30%;
    right: 15%;
    animation-delay: 2s;
}

.particle-4 {
    width: 10px;
    height: 10px;
    top: 70%;
    right: 25%;
    animation-delay: 3s;
}

.particle-5 {
    width: 14px;
    height: 14px;
    top: 15%;
    left: 60%;
    animation-delay: 4s;
}

.particle-6 {
    width: 8px;
    height: 8px;
    top: 80%;
    left: 70%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.shape {
    position: absolute;
    animation: float 6s ease-in-out infinite;
    opacity: 0.8;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
}

/* Medical-themed shapes */
.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    background: rgba(255, 255, 255, 0.15);
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4V1h-6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-8 11h-2v2H7v-2H5v-2h2v-2h2v2h2v2zm4-2h-2v-2h2v2zm2 4h-2v-2h2v2z'/%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4V1h-6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-8 11h-2v2H7v-2H5v-2h2v-2h2v2h2v2zm4-2h-2v-2h2v2zm2 4h-2v-2h2v2z'/%3E%3C/svg%3E");
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
    background: rgba(255, 255, 255, 0.15);
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 8h-1V3H6v5H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zM8 5h8v3H8V5zm8 14H8v-4h8v4zm4-4h-2v-2H6v2H4v-4c0-.55.45-1 1-1h14c.55 0 1 .45 1 1v4z'/%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 8h-1V3H6v5H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zM8 5h8v3H8V5zm8 14H8v-4h8v4zm4-4h-2v-2H6v2H4v-4c0-.55.45-1 1-1h14c.55 0 1 .45 1 1v4z'/%3E%3C/svg%3E");
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
    background: rgba(255, 255, 255, 0.15);
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M10.5 15H8v-3h2.5V9.5h3V12H16v3h-2.5v2.5h-3V15zM19 8v11c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2V8c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2zm-2 0H7v11h10V8zm1-5H6v2h12V3z'/%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M10.5 15H8v-3h2.5V9.5h3V12H16v3h-2.5v2.5h-3V15zM19 8v11c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2V8c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2zm-2 0H7v11h10V8zm1-5H6v2h12V3z'/%3E%3C/svg%3E");
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
    background: rgba(255, 255, 255, 0.15);
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 9c-1.65 0-3-1.35-3-3s1.35-3 3-3 3 1.35 3 3-1.35 3-3 3zm0-4c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1zm-6 5.99 3-3 1.5 1.5 3-3L18 15v3H6v-3.01z'/%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 9c-1.65 0-3-1.35-3-3s1.35-3 3-3 3 1.35 3 3-1.35 3-3 3zm0-4c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1zm-6 5.99 3-3 1.5 1.5 3-3L18 15v3H6v-3.01z'/%3E%3C/svg%3E");
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
}

[data-theme="dark"] .shape {
    opacity: 0.8;
    filter: drop-shadow(0 0 15px rgba(96, 165, 250, 0.3));
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(5deg); }
    50% { transform: translateY(-25px) rotate(0deg); }
    75% { transform: translateY(-10px) rotate(-5deg); }
}

/* Hero Container */
.hero-container {
    position: relative;
    z-index: 3;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
    min-height: 100vh;
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Hero Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 50px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    animation: fadeInUp 1s ease-out;
}

.hero-badge i {
    color: var(--orange-color);
    font-size: 0.8rem;
}

/* Hero Main Content */
.hero-main {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-logo-section {
    margin-bottom: 2rem;
}

.hero-logo-wrapper {
    position: relative;
    display: inline-block;
}

.hero-logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-glow 3s infinite ease-in-out;
    z-index: 1;
}

@keyframes pulse-glow {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 2rem;
    color: white;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.gradient-text {
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    display: inline-block;
}

.highlight-text {
    position: relative;
    color: white;
    display: inline-block;
}

.highlight-text::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
    opacity: 0.6;
    z-index: -1;
    border-radius: 4px;
}

.hero-description {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
    line-height: 1.7;
    animation: fadeInUp 1s ease-out 0.9s both;
}

.text-highlight {
    color: var(--orange-color);
    font-weight: 600;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 1.1s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--orange-color);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 1.3s both;
}

.hero-btn-primary {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
    border: none;
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

.hero-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 53, 0.4);
}

.hero-btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 14px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.hero-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.hero-btn-primary:hover .btn-shine {
    left: 100%;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
    color: white;
    box-shadow: var(--shadow-medium);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    background: linear-gradient(135deg, var(--orange-light) 0%, var(--orange-color) 100%);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-cards {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    position: relative;
    z-index: 2;
}

.hero-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 280px;
}

.hero-card:hover {
    transform: translateX(10px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.card-dental .card-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-medical .card-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.card-pharmacy .card-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.card-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    margin: 0 0 0.25rem 0;
}

.card-content p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.card-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.hero-card:hover .card-glow {
    opacity: 1;
}

.icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.icon-container:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.2);
}

.icon-container i {
    font-size: 3rem;
    color: white;
    margin-bottom: 0.5rem;
}

.icon-container span {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.dental { animation-delay: 0s; }
.medical { animation-delay: 0.5s; }
.pharmacy { animation-delay: 1s; }

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Hero Decoration */
.hero-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    animation: float 8s ease-in-out infinite;
}

.circle-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 60px;
    height: 60px;
    top: 70%;
    right: 20%;
    animation-delay: 2s;
}

.circle-3 {
    width: 80px;
    height: 80px;
    top: 40%;
    right: 5%;
    animation-delay: 4s;
}

/* Hero Scroll Indicator */
.hero-scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    animation: fadeInUp 1s ease-out 1.5s both;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 2s infinite;
}

.scroll-arrow i {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-family: 'Nexa', 'Inter', sans-serif;
    letter-spacing: 0.5px;
}

.section-title .gradient-text {
    font-family: 'Nexa', 'Inter', sans-serif;
    font-weight: 800;
    letter-spacing: 0.5px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Applications Section */
.applications {
    background: #003875;
    position: relative;
    overflow: hidden;
}

.applications::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 0.07;
    background:
        repeating-linear-gradient(
            45deg,
            var(--primary-color),
            var(--primary-color) 1.5px,
            transparent 1.5px,
            transparent 20px
        ),
        repeating-linear-gradient(
            -45deg,
            var(--accent-color),
            var(--accent-color) 1.5px,
            transparent 1.5px,
            transparent 20px
        );
    animation: applicationsGrid 15s linear infinite;
}

[data-theme="dark"] .applications::before {
    opacity: 0.06;
}

@keyframes applicationsGrid {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(20px) translateY(20px); }
}

.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.app-card {
    background: #4A73D3;
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid #003875;
    color: #FFFFFF;
}

.app-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

.app-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.dental-app::before { background: var(--gradient-primary); }
.medical-app::before { background: var(--gradient-secondary); }
.pharmacy-app::before { background: var(--gradient-accent); }

.app-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
}

.dental-app .app-icon { background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%); }
.medical-app .app-icon { background: linear-gradient(135deg, var(--orange-light) 0%, var(--orange-color) 100%); }
.pharmacy-app .app-icon { background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%); }

.app-icon i {
    font-size: 2rem;
    color: white;
}

.app-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.app-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.app-features {
    list-style: none;
    margin-bottom: 2rem;
}

.app-features li {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.app-features i {
    color: var(--orange-color);
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

.app-btn {
    width: 100%;
    padding: 12px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.dental-app .app-btn { background: var(--gradient-primary); }
.medical-app .app-btn { background: var(--gradient-secondary); }
.pharmacy-app .app-btn { background: var(--gradient-accent); }

.app-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Features Section */
.features {
    position: relative;
    overflow: hidden;
    background: #003875;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 0.07;
    background:
        repeating-linear-gradient(
            45deg,
            var(--primary-color),
            var(--primary-color) 1.5px,
            transparent 1.5px,
            transparent 20px
        ),
        repeating-linear-gradient(
            -45deg,
            var(--accent-color),
            var(--accent-color) 1.5px,
            transparent 1.5px,
            transparent 20px
        );
    animation: featuresGrid 15s linear infinite;
}

[data-theme="dark"] .features::before {
    opacity: 0.06;
}

@keyframes featuresGrid {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(20px) translateY(20px); }
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 1;
}

.feature-card {
    background: #4A73D3;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: var(--transition);
    border: 1px solid #003875;
    color: #FFFFFF;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: var(--transition);
}

.feature-card:nth-child(2n) .feature-icon {
    background: linear-gradient(135deg, var(--orange-light) 0%, var(--orange-color) 100%);
}

.feature-card:nth-child(3n) .feature-icon {
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
}

.feature-card:nth-child(4n) .feature-icon {
    background: linear-gradient(135deg, var(--orange-light) 0%, var(--orange-color) 100%);
}

.feature-card:nth-child(5n) .feature-icon {
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
}

.feature-card:nth-child(6n) .feature-icon {
    background: linear-gradient(135deg, var(--orange-light) 0%, var(--orange-color) 100%);
}

.feature-icon i {
    font-size: 1.8rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact Section */
.contact {
    background: #003875;
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 0.08;
    background:
        radial-gradient(circle at 25% 25%, var(--accent-color) 2.5px, transparent 2.5px),
        radial-gradient(circle at 75% 75%, var(--secondary-color) 1.5px, transparent 1.5px),
        radial-gradient(circle at 50% 50%, var(--primary-color) 2px, transparent 2px);
    background-size: 100px 100px, 60px 60px, 80px 80px;
    background-position: 0 0, 30px 30px, 50px 50px;
    animation: contactPattern 20s linear infinite;
}

.contact::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    background:
        linear-gradient(45deg, transparent 40%, rgba(37, 99, 235, 0.06) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(6, 182, 212, 0.06) 50%, transparent 60%);
    background-size: 120px 120px;
    animation: contactFlow 25s ease-in-out infinite;
}

[data-theme="dark"] .contact::before {
    opacity: 0.08;
}

[data-theme="dark"] .contact::after {
    background:
        linear-gradient(45deg, transparent 40%, rgba(96, 165, 250, 0.05) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(34, 211, 238, 0.05) 50%, transparent 60%);
}

@keyframes contactPattern {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-100px) translateY(-100px); }
}

@keyframes contactFlow {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: #4A73D3;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border: 1px solid #003875;
    color: #FFFFFF;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.contact-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-item:nth-child(2) .contact-icon {
    background: linear-gradient(135deg, var(--orange-light) 0%, var(--orange-color) 100%);
}

.contact-item:nth-child(3) .contact-icon {
    background: linear-gradient(135deg, var(--orange-color) 0%, var(--orange-light) 100%);
}

.contact-icon i {
    font-size: 1.5rem;
    color: white;
}

.contact-details h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.social-media {
    background: #4A73D3;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    text-align: center;
    border: 1px solid #003875;
    color: #FFFFFF;
}

.social-media h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: var(--transition);
    border: 2px solid transparent;
}

.social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: white;
}

.social-link.facebook {
    background: #1877f2;
    color: white;
}

.social-link.whatsapp {
    background: #25D366;
    color: white;
}

.social-link.gmail {
    background: #EA4335;
    color: white;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.social-link i {
    font-size: 1.2rem;
}

.social-link span {
    font-weight: 500;
}

/* Footer */
.footer {
    background: var(--footer-bg);
    color: var(--footer-text);
    padding: 2rem 0;
    text-align: center;
    transition: background-color var(--transition), color var(--transition);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--footer-text);
    transition: color var(--transition);
}

.footer-logo span {
    font-family: 'Nexa', 'Inter', sans-serif;
    font-weight: 800;
    letter-spacing: 0.5px;
}

.footer-logo .logo-img {
    width: 40px;
    height: 40px;
    margin-right: 8px;
    transition: transform 0.3s ease;
    object-fit: contain;
    filter: brightness(1.2);
}

.footer-logo:hover .logo-img {
    transform: scale(1.1);
    filter: brightness(1.5);
}

.footer p {
    color: var(--text-secondary);
    transition: color var(--transition);
}

/* Responsive Design */
@media (max-width: 768px) {
    .translate-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .translate-btn .translate-text {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 75px;
        flex-direction: column;
        background-color: rgba(0, 56, 117, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-medium);
        backdrop-filter: blur(20px);
        padding: 2rem 0;
        gap: 1rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
        padding: 0 20px;
    }

    .hero-logo {
        width: 80px;
        height: 80px;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .hero-stats {
        gap: 2rem;
        justify-content: center;
    }

    .stat-number {
        font-size: 2rem;
    }

    .hero-actions {
        justify-content: center;
    }

    .hero-cards {
        flex-direction: row;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .hero-card {
        min-width: 200px;
        flex: 1;
        max-width: 250px;
    }

    .applications-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .social-links {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .social-link {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-container {
        gap: 2rem;
        padding: 0 16px;
    }

    .hero-logo {
        width: 60px;
        height: 60px;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-stats {
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }

    .hero-cards {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-card {
        min-width: auto;
        max-width: none;
    }

    .hero-scroll-indicator {
        bottom: 1rem;
    }

    .app-card {
        padding: 1.5rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .social-links {
        flex-direction: column;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Floating Medical Elements */
.floating-medical-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.medical-element {
    position: absolute;
    color: var(--primary-color);
    font-size: 1.8rem;
    opacity: 0.2;
    animation: medicalFloat 8s ease-in-out infinite;
    transition: opacity var(--transition), color var(--transition);
    filter: drop-shadow(0 0 3px rgba(37, 99, 235, 0.3));
}

[data-theme="dark"] .medical-element {
    opacity: 0.25;
    color: var(--accent-color);
    filter: drop-shadow(0 0 5px rgba(34, 211, 238, 0.4));
}

.element-1 {
    top: 15%;
    left: 5%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.element-2 {
    top: 25%;
    right: 10%;
    animation-delay: 2s;
    animation-duration: 10s;
}

.element-3 {
    bottom: 30%;
    left: 8%;
    animation-delay: 4s;
    animation-duration: 9s;
}

.element-4 {
    top: 60%;
    right: 15%;
    animation-delay: 1s;
    animation-duration: 11s;
}

.element-5 {
    bottom: 15%;
    right: 5%;
    animation-delay: 3s;
    animation-duration: 7s;
}

@keyframes medicalFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.2;
    }
    25% {
        transform: translateY(-20px) rotate(90deg) scale(1.1);
        opacity: 0.35;
    }
    50% {
        transform: translateY(-30px) rotate(180deg) scale(0.9);
        opacity: 0.25;
    }
    75% {
        transform: translateY(-15px) rotate(270deg) scale(1.05);
        opacity: 0.4;
    }
}

[data-theme="dark"] @keyframes medicalFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.15;
    }
    25% {
        transform: translateY(-20px) rotate(90deg) scale(1.1);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-30px) rotate(180deg) scale(0.9);
        opacity: 0.2;
    }
    75% {
        transform: translateY(-15px) rotate(270deg) scale(1.05);
        opacity: 0.35;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }
